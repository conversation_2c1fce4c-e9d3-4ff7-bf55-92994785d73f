<?php

declare(strict_types=1);

namespace App\Domain\Cycling\Result\Command;

use App\Domain\Cycling\Result\Repository\ResultCyclistRepository;
use App\Domain\Cycling\Result\Resolver\ResultCyclistResolver;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
final readonly class DeleteResultCyclistCommandHandler
{
    public function __construct(
        private ResultCyclistRepository $resultCyclistRepository,
        private ResultCyclistResolver $resultCyclistResolver,
    ) {
    }

    public function __invoke(DeleteResultCyclistCommand $command): void
    {
        $resultCyclist = $this->resultCyclistResolver->resolve($command->id);
        $this->resultCyclistRepository->delete($resultCyclist);
    }
}
