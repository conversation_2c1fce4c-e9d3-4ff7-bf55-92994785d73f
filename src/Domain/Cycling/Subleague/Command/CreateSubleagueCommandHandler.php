<?php

declare(strict_types=1);

namespace App\Domain\Cycling\Subleague\Command;

use App\Domain\Cycling\Subleague\Repository\SubleagueRepository;
use App\Domain\Cycling\Subleague\Subleague;
use App\Domain\Cycling\Tournament\Resolver\TournamentResolver;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
final readonly class CreateSubleagueCommandHandler
{
    public function __construct(
        private SubleagueRepository $subleagueRepository,
        private TournamentResolver $tournamentResolver,
    ) {
    }

    public function __invoke(CreateSubleagueCommand $command): Subleague
    {
        $tournament = $this->tournamentResolver->resolve($command->tournamentId);

        $subleague = new Subleague(
            $command->name,
            $tournament,
        );

        $this->subleagueRepository->save($subleague);

        return $subleague;
    }
}
