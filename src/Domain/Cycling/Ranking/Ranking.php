<?php

declare(strict_types=1);

namespace App\Domain\Cycling\Ranking;

use App\Domain\Cycling\Tournament\Tournament;
use App\Domain\TimestampableEntity;
use DateTimeImmutable;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity]
#[ORM\Table(name: 'cycling_ranking')]
class Ranking
{
    use TimestampableEntity;

    #[ORM\Id]
    #[ORM\Column(type: Types::INTEGER, options: ['unsigned' => true])]
    #[ORM\GeneratedValue]
    private ?int $id = null;

    #[ORM\Column(type: Types::STRING)]
    private string $name;

    #[ORM\ManyToOne(targetEntity: Tournament::class, inversedBy: 'rankings')]
    #[ORM\JoinColumn(onDelete: 'CASCADE')]
    private Tournament $tournament;

    public function __construct(
        string $name,
        Tournament $tournament,
    ) {
        $this->name = $name;
        $this->tournament = $tournament;
        $this->createdAt = new DateTimeImmutable();
    }

    public function update(
        string $name,
        Tournament $tournament,
    ): void {
        $this->name = $name;
        $this->tournament = $tournament;
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function getTournament(): Tournament
    {
        return $this->tournament;
    }

    public function setTournament(Tournament $tournament): void
    {
        $this->tournament = $tournament;
    }

    public function getCreatedAt(): DateTimeImmutable
    {
        return $this->createdAt;
    }

    public function getUpdatedAt(): ?DateTimeImmutable
    {
        return $this->updatedAt;
    }

    public function __toString(): string
    {
        return $this->name;
    }
}
