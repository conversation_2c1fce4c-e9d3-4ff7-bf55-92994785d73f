<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250602183814 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            CREATE TABLE cycling_ranking_type (id INT UNSIGNED AUTO_INCREMENT NOT NULL, name VARCHAR(255) NOT NULL, points LONGTEXT NOT NULL, allow_result_prices TINYINT(1) NOT NULL, created_at DATETIME NOT NULL, updated_at DATETIME DEFAULT NULL, ranking_id INT UNSIGNED DEFAULT NULL, INDEX IDX_10EE377220F64684 (ranking_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE cycling_ranking_type ADD CONSTRAINT FK_10EE377220F64684 FOREIGN KEY (ranking_id) REFERENCES cycling_ranking (id) ON DELETE CASCADE
        SQL);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            ALTER TABLE cycling_ranking_type DROP FOREIGN KEY FK_10EE377220F64684
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE cycling_ranking_type
        SQL);
    }
}
