<?php

declare(strict_types=1);

namespace App\Infrastructure\Repository;

use App\Domain\Cycling\Ranking\Ranking;
use App\Domain\Cycling\Ranking\Repository\RankingRepository as RankingRepositoryInterface;
use Doctrine\ORM\EntityManagerInterface;

final readonly class RankingRepository implements RankingRepositoryInterface
{
    public function __construct(
        private EntityManagerInterface $entityManager,
    ) {
    }

    public function find(int $id): ?Ranking
    {
        return $this->entityManager->getRepository(Ranking::class)->find($id);
    }

    public function save(Ranking $ranking): void
    {
        $this->entityManager->persist($ranking);
        $this->entityManager->flush();
    }
}
