<?php

declare(strict_types=1);

namespace App\Domain\Cycling\Ranking\Resolver;

use App\Domain\Assert;
use App\Domain\Cycling\Ranking\RankingType;
use App\Domain\Cycling\Ranking\Repository\RankingTypeRepository;

final readonly class RankingTypeResolver
{
    public function __construct(private RankingTypeRepository $repository)
    {
    }

    public function resolve(int $id): RankingType
    {
        $rankingType = $this->repository->find($id);

        Assert::isInstanceOf($rankingType, RankingType::class, 'ranking type not found');

        return $rankingType;
    }
}
