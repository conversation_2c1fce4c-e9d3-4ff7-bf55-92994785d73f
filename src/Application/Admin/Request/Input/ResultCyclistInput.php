<?php

declare(strict_types=1);

namespace App\Application\Admin\Request\Input;

use App\Domain\Cycling\Cyclist\Cyclist;
use App\Domain\Cycling\Result\Result;
use App\Domain\Cycling\Result\ResultCyclist;
use Symfony\Component\Validator\Constraints as Assert;

final class ResultCyclistInput
{
    public ?int $id = null;

    #[Assert\NotBlank]
    #[Assert\Type(type: 'integer')]
    #[Assert\Positive]
    public int $position;

    #[Assert\Type(type: 'integer')]
    #[Assert\PositiveOrZero]
    public ?int $points = null;

    #[Assert\NotBlank]
    public ?Cyclist $cyclist = null;

    public ?Result $result = null;

    public static function fromEntity(ResultCyclist $resultCyclist): self
    {
        $input = new self();
        $input->id = $resultCyclist->getId();
        $input->position = $resultCyclist->getPosition();
        $input->points = $resultCyclist->getPoints();
        $input->cyclist = $resultCyclist->getCyclist();
        $input->result = $resultCyclist->getResult();

        return $input;
    }
}
