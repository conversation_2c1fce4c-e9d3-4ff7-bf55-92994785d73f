<?php

declare(strict_types=1);

namespace App\Application\Admin\Admin\Cycling;

use App\Application\Admin\Controller\CRUDController;
use App\Application\Admin\LifecycleMiddlewareInterface;
use App\Application\Admin\LifecycleMiddlewareTrait;
use App\Application\Admin\MessageBusAwareTrait;
use App\Application\Admin\Request\Input\RankingInput;
use App\Application\Admin\UnderscoreGlobalLabelStrategy;
use App\Domain\Cycling\Ranking\Command\CreateRankingCommand;
use App\Domain\Cycling\Ranking\Command\UpdateRankingCommand;
use App\Domain\Cycling\Ranking\Ranking;
use App\Domain\Cycling\Tournament\Tournament;
use Sonata\AdminBundle\Admin\AbstractAdmin;
use Sonata\AdminBundle\Datagrid\ListMapper;
use Sonata\AdminBundle\Form\FormMapper;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\DependencyInjection\Attribute\Autoconfigure;

/**
 * @extends AbstractAdmin<Ranking>
 *
 * @implements LifecycleMiddlewareInterface<Ranking, RankingInput>
 *
 * @method Ranking getSubject()
 */
#[Autoconfigure(
    tags: [
        [
            'controller' => CRUDController::class,
            'label' => 'global.label_ranking',
            'label_translator_strategy' => UnderscoreGlobalLabelStrategy::class,
            'manager_type' => 'orm',
            'model_class' => Ranking::class,
            'name' => 'sonata.admin',
            'group' => 'admin.group.cycling',
            'translation_domain' => 'admin',
            'show_in_dashboard' => false,
        ],
    ],
    calls: [
        ['setMessageBus', ['@Symfony\\Component\\Messenger\\MessageBusInterface']],
        ['addChild', ['@App\Application\Admin\Admin\Cycling\RankingTypeAdmin', 'ranking']],
    ],
)]
final class RankingAdmin extends AbstractAdmin implements LifecycleMiddlewareInterface
{
    /** @use LifecycleMiddlewareTrait<Ranking, RankingInput> */
    use LifecycleMiddlewareTrait;
    use MessageBusAwareTrait;

    protected function generateBaseRouteName(bool $isChildAdmin = false): string
    {
        return 'admin_ranking';
    }

    protected function generateBaseRoutePattern(bool $isChildAdmin = false): string
    {
        return 'ranking';
    }

    protected function configureListFields(ListMapper $list): void
    {
        $list
            ->addIdentifier('name')
            ->add('tournament')
            ->add('createdAt');
    }

    protected function configureFormFields(FormMapper $form): void
    {
        $isChild = $this->isChild();

        $form
            ->with('global.general', ['class' => 'col-md-6'])
            ->add('name')
            ->add('tournament', EntityType::class, [
                'class' => Tournament::class,
                'disabled' => $isChild,
                'choice_label' => 'name',
            ])
            ->end();

        if ($isChild) {
            $form->get('tournament')->setData($this->getParent()->getSubject());
        }
    }

    public function lifecycleMiddlewareDispatchCreatable(object $creatable): object
    {
        return $this->lifecycleMiddlewareDispatchMessage($creatable);
    }

    public function lifecycleMiddlewareDispatchEditable(object $editable): object
    {
        return $this->lifecycleMiddlewareDispatchMessage($editable);
    }

    public function lifecycleMiddlewareTransformToCreatable(object $submittedData): object
    {
        // @phpstan-ignore-next-line
        return new CreateRankingCommand(
            $submittedData->name,
            $this->getTournamentId($submittedData),
        );
    }

    public function lifecycleMiddlewareTransformToEditable(object $submittedData): object
    {
        $ranking = $this->getSubject();

        // @phpstan-ignore-next-line
        return new UpdateRankingCommand(
            $ranking->getId(),
            $submittedData->name,
            $this->getTournamentId($submittedData),
        );
    }

    public function lifecycleMiddlewareFormDataClass(): string
    {
        return RankingInput::class;
    }

    public function lifecycleMiddlewareTransformToFormDataClass(object $entity): object
    {
        return RankingInput::createFromEntity($entity);
    }

    private function getTournamentId(object $submittedData): ?int
    {
        if ($this->isChild()) {
            $tournament = $this->getParent()->getSubject();
            if ($tournament instanceof Tournament) {
                return $tournament->getId();
            }
        }

        return $submittedData->tournament?->getId() ?? null;
    }
}
