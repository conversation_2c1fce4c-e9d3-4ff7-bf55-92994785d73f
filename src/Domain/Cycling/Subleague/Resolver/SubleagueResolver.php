<?php

declare(strict_types=1);

namespace App\Domain\Cycling\Subleague\Resolver;

use App\Domain\Assert;
use App\Domain\Cycling\Subleague\Repository\SubleagueRepository;
use App\Domain\Cycling\Subleague\Subleague;

final readonly class SubleagueResolver
{
    public function __construct(private SubleagueRepository $repository)
    {
    }

    public function resolve(int $id): Subleague
    {
        $subleague = $this->repository->find($id);

        Assert::isInstanceOf($subleague, Subleague::class, 'subleague not found');

        return $subleague;
    }
}
