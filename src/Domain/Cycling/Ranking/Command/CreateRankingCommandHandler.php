<?php

declare(strict_types=1);

namespace App\Domain\Cycling\Ranking\Command;

use App\Domain\Cycling\Ranking\Ranking;
use App\Domain\Cycling\Ranking\Repository\RankingRepository;
use App\Domain\Cycling\Tournament\Resolver\TournamentResolver;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
final readonly class CreateRankingCommandHandler
{
    public function __construct(
        private RankingRepository $rankingRepository,
        private TournamentResolver $tournamentResolver,
    ) {
    }

    public function __invoke(CreateRankingCommand $command): Ranking
    {
        $tournament = $this->tournamentResolver->resolve($command->tournamentId);

        $ranking = new Ranking(
            $command->name,
            $tournament,
        );

        $this->rankingRepository->save($ranking);

        return $ranking;
    }
}
