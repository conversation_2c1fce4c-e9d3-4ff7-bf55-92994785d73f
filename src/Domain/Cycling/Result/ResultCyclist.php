<?php

declare(strict_types=1);

namespace App\Domain\Cycling\Result;

use App\Domain\Cycling\Cyclist\Cyclist;
use App\Domain\TimestampableEntity;
use DateTimeImmutable;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity]
#[ORM\Table(name: 'cycling_result_cyclist')]
class ResultCyclist
{
    use TimestampableEntity;

    #[ORM\Id]
    #[ORM\Column(type: Types::INTEGER, options: ['unsigned' => true])]
    #[ORM\GeneratedValue]
    private ?int $id = null;

    #[ORM\Column(type: Types::INTEGER)]
    private int $position;

    #[ORM\Column(type: Types::INTEGER, nullable: true)]
    private ?int $points;

    #[ORM\ManyToOne(targetEntity: Result::class, inversedBy: 'cyclists')]
    #[ORM\JoinColumn(onDelete: 'CASCADE')]
    private ?Result $result;

    #[ORM\ManyToOne(targetEntity: Cyclist::class)]
    #[ORM\JoinColumn(onDelete: 'CASCADE')]
    private Cyclist $cyclist;

    public function __construct(
        int $position,
        Cyclist $cyclist,
        ?int $points = null,
        ?Result $result = null,
    ) {
        $this->position = $position;
        $this->cyclist = $cyclist;
        $this->points = $points;
        $this->result = $result;
        $this->createdAt = new DateTimeImmutable();
    }

    public function update(
        int $position,
        Cyclist $cyclist,
        ?int $points = null,
    ): void {
        $this->position = $position;
        $this->cyclist = $cyclist;
        $this->points = $points;
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getPosition(): int
    {
        return $this->position;
    }

    public function getPoints(): ?int
    {
        return $this->points;
    }

    public function getResult(): ?Result
    {
        return $this->result;
    }

    public function setResult(?Result $result): void
    {
        $this->result = $result;
    }

    public function getCyclist(): Cyclist
    {
        return $this->cyclist;
    }

    public function setCyclist(Cyclist $cyclist): void
    {
        $this->cyclist = $cyclist;
    }

    public function getCreatedAt(): DateTimeImmutable
    {
        return $this->createdAt;
    }

    public function getUpdatedAt(): ?DateTimeImmutable
    {
        return $this->updatedAt;
    }

    public function __toString(): string
    {
        return sprintf('%d. %s', $this->position, $this->cyclist->getFullName());
    }
}
