<?php

declare(strict_types=1);

namespace App\Domain\Cycling\Ranking\Command;

use App\Domain\Cycling\Ranking\RankingType;
use App\Domain\Cycling\Ranking\Repository\RankingTypeRepository;
use App\Domain\Cycling\Ranking\Resolver\RankingResolver;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
final readonly class CreateRankingTypeCommandHandler
{
    public function __construct(
        private RankingTypeRepository $rankingTypeRepository,
        private RankingResolver $rankingResolver,
    ) {
    }

    public function __invoke(CreateRankingTypeCommand $command): RankingType
    {
        $ranking = $this->rankingResolver->resolve($command->rankingId);

        $rankingType = new RankingType(
            $command->name,
            $command->points,
            $command->allowResultPrices,
            $ranking,
        );

        $this->rankingTypeRepository->save($rankingType);

        return $rankingType;
    }
}
