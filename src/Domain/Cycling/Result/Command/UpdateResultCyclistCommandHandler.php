<?php

declare(strict_types=1);

namespace App\Domain\Cycling\Result\Command;

use App\Domain\Cycling\Cyclist\Resolver\CyclistResolver;
use App\Domain\Cycling\Result\Repository\ResultCyclistRepository;
use App\Domain\Cycling\Result\Resolver\ResultCyclistResolver;
use App\Domain\Cycling\Result\ResultCyclist;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
final readonly class UpdateResultCyclistCommandHandler
{
    public function __construct(
        private ResultCyclistRepository $resultCyclistRepository,
        private ResultCyclistResolver $resultCyclistResolver,
        private CyclistResolver $cyclistResolver,
    ) {
    }

    public function __invoke(UpdateResultCyclistCommand $command): ResultCyclist
    {
        $resultCyclist = $this->resultCyclistResolver->resolve($command->id);
        $cyclist = $this->cyclistResolver->resolve($command->cyclistId);

        $resultCyclist->update(
            $command->position,
            $cyclist,
            $command->points,
        );

        $this->resultCyclistRepository->save($resultCyclist);

        return $resultCyclist;
    }
}
