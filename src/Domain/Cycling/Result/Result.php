<?php

declare(strict_types=1);

namespace App\Domain\Cycling\Result;

use App\Domain\Cycling\Ranking\RankingType;
use App\Domain\Cycling\Stage\Stage;
use App\Domain\TimestampableEntity;
use DateTimeImmutable;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity]
#[ORM\Table(name: 'cycling_result')]
class Result
{
    use TimestampableEntity;

    #[ORM\Id]
    #[ORM\Column(type: Types::INTEGER, options: ['unsigned' => true])]
    #[ORM\GeneratedValue]
    private ?int $id = null;

    #[ORM\Column(type: Types::INTEGER)]
    private int $number;

    #[ORM\ManyToOne(targetEntity: RankingType::class, inversedBy: 'results')]
    #[ORM\JoinColumn(onDelete: 'CASCADE')]
    private RankingType $rankingType;

    #[ORM\ManyToOne(targetEntity: Stage::class, inversedBy: 'results')]
    #[ORM\JoinColumn(onDelete: 'CASCADE', nullable: true)]
    private ?Stage $stage;

    /**
     * @var Collection<int, ResultCyclist>
     */
    #[ORM\OneToMany(targetEntity: ResultCyclist::class, mappedBy: 'result', cascade: ['persist', 'remove'])]
    #[ORM\OrderBy(['position' => 'ASC'])]
    private Collection $cyclists;

    public function __construct(
        int $number,
        RankingType $rankingType,
        ?Stage $stage = null,
    ) {
        $this->number = $number;
        $this->rankingType = $rankingType;
        $this->stage = $stage;
        $this->cyclists = new ArrayCollection();
        $this->createdAt = new DateTimeImmutable();
    }

    public function update(
        int $number,
        RankingType $rankingType,
        ?Stage $stage = null,
    ): void {
        $this->number = $number;
        $this->rankingType = $rankingType;
        $this->stage = $stage;
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getNumber(): int
    {
        return $this->number;
    }

    public function getRankingType(): RankingType
    {
        return $this->rankingType;
    }

    public function setRankingType(RankingType $rankingType): void
    {
        $this->rankingType = $rankingType;
    }

    public function getStage(): ?Stage
    {
        return $this->stage;
    }

    public function setStage(?Stage $stage): void
    {
        $this->stage = $stage;
    }

    /**
     * @return Collection<int, ResultCyclist>
     */
    public function getCyclists(): Collection
    {
        return $this->cyclists;
    }

    public function addCyclist(ResultCyclist $cyclist): void
    {
        if (!$this->cyclists->contains($cyclist)) {
            $this->cyclists->add($cyclist);
            $cyclist->setResult($this);
        }
    }

    public function removeCyclist(ResultCyclist $cyclist): void
    {
        if ($this->cyclists->removeElement($cyclist)) {
            if ($cyclist->getResult() === $this) {
                $cyclist->setResult(null);
            }
        }
    }

    public function getCreatedAt(): DateTimeImmutable
    {
        return $this->createdAt;
    }

    public function getUpdatedAt(): ?DateTimeImmutable
    {
        return $this->updatedAt;
    }

    public function __toString(): string
    {
        return sprintf('Result #%d', $this->number);
    }
}
