<?php

declare(strict_types=1);

namespace App\Application\Admin\Admin\Cycling;

use App\Application\Admin\Controller\CRUDController;
use App\Application\Admin\LifecycleMiddlewareInterface;
use App\Application\Admin\LifecycleMiddlewareTrait;
use App\Application\Admin\MessageBusAwareTrait;
use App\Application\Admin\Request\Input\SubleagueInput;
use App\Application\Admin\UnderscoreGlobalLabelStrategy;
use App\Domain\Cycling\Subleague\Command\CreateSubleagueCommand;
use App\Domain\Cycling\Subleague\Command\UpdateSubleagueCommand;
use App\Domain\Cycling\Subleague\Subleague;
use App\Domain\Cycling\Tournament\Tournament;
use Sonata\AdminBundle\Admin\AbstractAdmin;
use Sonata\AdminBundle\Datagrid\ListMapper;
use Sonata\AdminBundle\Form\FormMapper;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\DependencyInjection\Attribute\Autoconfigure;

/**
 * @extends AbstractAdmin<Subleague>
 *
 * @implements LifecycleMiddlewareInterface<Subleague, SubleagueInput>
 *
 * @method Subleague getSubject()
 */
#[Autoconfigure(
    tags: [
        [
            'controller' => CRUDController::class,
            'label' => 'global.label_subleague',
            'label_translator_strategy' => UnderscoreGlobalLabelStrategy::class,
            'manager_type' => 'orm',
            'model_class' => Subleague::class,
            'name' => 'sonata.admin',
            'group' => 'admin.group.cycling',
            'translation_domain' => 'admin',
        ],
    ],
    calls: [
        ['setMessageBus', ['@Symfony\\Component\\Messenger\\MessageBusInterface']],
    ],
)]
final class SubleagueAdmin extends AbstractAdmin implements LifecycleMiddlewareInterface
{
    /** @use LifecycleMiddlewareTrait<Subleague, SubleagueInput> */
    use LifecycleMiddlewareTrait;
    use MessageBusAwareTrait;

    protected function generateBaseRouteName(bool $isChildAdmin = false): string
    {
        return 'admin_subleague';
    }

    protected function generateBaseRoutePattern(bool $isChildAdmin = false): string
    {
        return 'subleague';
    }

    protected function configureListFields(ListMapper $list): void
    {
        $list
            ->addIdentifier('name')
            ->add('tournament')
            ->add('createdAt');
    }

    protected function configureFormFields(FormMapper $form): void
    {
        $isChild = $this->isChild();

        $form
            ->with('global.general', ['class' => 'col-md-6'])
            ->add('name')
            ->add('tournament', EntityType::class, [
                'class' => Tournament::class,
                'disabled' => $isChild,
                'choice_label' => 'name',
            ])
            ->end();

        if ($isChild) {
            $form->get('tournament')->setData($this->getParent()->getSubject());
        }
    }

    public function lifecycleMiddlewareDispatchCreatable(object $creatable): object
    {
        return $this->lifecycleMiddlewareDispatchMessage($creatable);
    }

    public function lifecycleMiddlewareDispatchEditable(object $editable): object
    {
        return $this->lifecycleMiddlewareDispatchMessage($editable);
    }

    public function lifecycleMiddlewareTransformToCreatable(object $submittedData): object
    {
        // @phpstan-ignore-next-line
        return new CreateSubleagueCommand(
            $submittedData->name,
            $this->getTournamentId($submittedData),
        );
    }

    public function lifecycleMiddlewareTransformToEditable(object $submittedData): object
    {
        $subleague = $this->getSubject();

        // @phpstan-ignore-next-line
        return new UpdateSubleagueCommand(
            $subleague->getId(),
            $submittedData->name,
            $this->getTournamentId($submittedData),
        );
    }

    public function lifecycleMiddlewareFormDataClass(): string
    {
        return SubleagueInput::class;
    }

    public function lifecycleMiddlewareTransformToFormDataClass(object $entity): object
    {
        return SubleagueInput::createFromEntity($entity);
    }

    private function getTournamentId(object $submittedData): ?int
    {
        if ($this->isChild()) {
            $tournament = $this->getParent()->getSubject();
            if ($tournament instanceof Tournament) {
                return $tournament->getId();
            }
        }

        return $submittedData->tournament?->getId() ?? null;
    }
}
