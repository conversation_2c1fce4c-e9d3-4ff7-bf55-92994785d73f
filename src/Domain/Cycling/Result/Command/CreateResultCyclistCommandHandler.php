<?php

declare(strict_types=1);

namespace App\Domain\Cycling\Result\Command;

use App\Domain\Cycling\Cyclist\Resolver\CyclistResolver;
use App\Domain\Cycling\Result\Repository\ResultCyclistRepository;
use App\Domain\Cycling\Result\Resolver\ResultResolver;
use App\Domain\Cycling\Result\ResultCyclist;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
final readonly class CreateResultCyclistCommandHandler
{
    public function __construct(
        private ResultCyclistRepository $resultCyclistRepository,
        private ResultResolver $resultResolver,
        private CyclistResolver $cyclistResolver,
    ) {
    }

    public function __invoke(CreateResultCyclistCommand $command): ResultCyclist
    {
        $result = $this->resultResolver->resolve($command->resultId);
        $cyclist = $this->cyclistResolver->resolve($command->cyclistId);

        $resultCyclist = new ResultCyclist(
            $command->position,
            $cyclist,
            $command->points,
            $result,
        );

        $result->addCyclist($resultCyclist);
        $this->resultCyclistRepository->save($resultCyclist);

        return $resultCyclist;
    }
}
