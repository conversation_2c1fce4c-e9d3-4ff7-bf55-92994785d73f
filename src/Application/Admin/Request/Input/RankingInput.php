<?php

declare(strict_types=1);

namespace App\Application\Admin\Request\Input;

use App\Domain\Cycling\Ranking\Ranking;
use App\Domain\Cycling\Tournament\Tournament;
use Symfony\Component\Validator\Constraints as Assert;

final class RankingInput
{
    #[Assert\NotBlank]
    #[Assert\Length(max: 255)]
    public ?string $name = null;

    #[Assert\NotBlank]
    public ?Tournament $tournament = null;

    public static function createFromEntity(Ranking $ranking): self
    {
        $input = new self();
        $input->name = $ranking->getName();
        $input->tournament = $ranking->getTournament();

        return $input;
    }
}
