<?php

declare(strict_types=1);

namespace App\Infrastructure\Repository;

use App\Domain\Cycling\Ranking\RankingType;
use App\Domain\Cycling\Ranking\Repository\RankingTypeRepository as RankingTypeRepositoryInterface;
use Doctrine\ORM\EntityManagerInterface;

final readonly class RankingTypeRepository implements RankingTypeRepositoryInterface
{
    public function __construct(
        private EntityManagerInterface $entityManager,
    ) {
    }

    public function find(int $id): ?RankingType
    {
        return $this->entityManager->getRepository(RankingType::class)->find($id);
    }

    public function save(RankingType $rankingType): void
    {
        $this->entityManager->persist($rankingType);
        $this->entityManager->flush();
    }
}
