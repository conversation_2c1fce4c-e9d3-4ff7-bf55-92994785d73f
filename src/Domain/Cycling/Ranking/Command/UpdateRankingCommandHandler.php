<?php

declare(strict_types=1);

namespace App\Domain\Cycling\Ranking\Command;

use App\Domain\Cycling\Ranking\Ranking;
use App\Domain\Cycling\Ranking\Repository\RankingRepository;
use App\Domain\Cycling\Ranking\Resolver\RankingResolver;
use App\Domain\Cycling\Tournament\Resolver\TournamentResolver;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
final readonly class UpdateRankingCommandHandler
{
    public function __construct(
        private RankingRepository $rankingRepository,
        private RankingResolver $rankingResolver,
        private TournamentResolver $tournamentResolver,
    ) {
    }

    public function __invoke(UpdateRankingCommand $command): Ranking
    {
        $ranking = $this->rankingResolver->resolve($command->id);
        $tournament = $this->tournamentResolver->resolve($command->tournamentId);

        $ranking->update(
            $command->name,
            $tournament,
        );

        $this->rankingRepository->save($ranking);

        return $ranking;
    }
}
