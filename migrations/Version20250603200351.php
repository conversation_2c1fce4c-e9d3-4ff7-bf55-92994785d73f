<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250603200351 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            CREATE TABLE cycling_result (id INT UNSIGNED AUTO_INCREMENT NOT NULL, number INT NOT NULL, created_at DATETIME NOT NULL, updated_at DATETIME DEFAULT NULL, ranking_type_id INT UNSIGNED DEFAULT NULL, stage_id INT DEFAULT NULL, INDEX IDX_D2C3A9ECD9DDAF9 (ranking_type_id), INDEX IDX_D2C3A9E2298D193 (stage_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE cycling_result_cyclist (id INT UNSIGNED AUTO_INCREMENT NOT NULL, position INT NOT NULL, points INT DEFAULT NULL, created_at DATETIME NOT NULL, updated_at DATETIME DEFAULT NULL, result_id INT UNSIGNED DEFAULT NULL, cyclist_id INT DEFAULT NULL, INDEX IDX_7AAE43C87A7B643 (result_id), INDEX IDX_7AAE43C87BFCCC26 (cyclist_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE cycling_result ADD CONSTRAINT FK_D2C3A9ECD9DDAF9 FOREIGN KEY (ranking_type_id) REFERENCES cycling_ranking_type (id) ON DELETE CASCADE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE cycling_result ADD CONSTRAINT FK_D2C3A9E2298D193 FOREIGN KEY (stage_id) REFERENCES cycling_stage (id) ON DELETE CASCADE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE cycling_result_cyclist ADD CONSTRAINT FK_7AAE43C87A7B643 FOREIGN KEY (result_id) REFERENCES cycling_result (id) ON DELETE CASCADE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE cycling_result_cyclist ADD CONSTRAINT FK_7AAE43C87BFCCC26 FOREIGN KEY (cyclist_id) REFERENCES cycling_cyclist (id) ON DELETE CASCADE
        SQL);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            ALTER TABLE cycling_result DROP FOREIGN KEY FK_D2C3A9ECD9DDAF9
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE cycling_result DROP FOREIGN KEY FK_D2C3A9E2298D193
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE cycling_result_cyclist DROP FOREIGN KEY FK_7AAE43C87A7B643
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE cycling_result_cyclist DROP FOREIGN KEY FK_7AAE43C87BFCCC26
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE cycling_result
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE cycling_result_cyclist
        SQL);
    }
}
