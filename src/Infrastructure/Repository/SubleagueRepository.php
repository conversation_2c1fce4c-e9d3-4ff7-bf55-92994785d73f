<?php

declare(strict_types=1);

namespace App\Infrastructure\Repository;

use App\Domain\Cycling\Subleague\Repository\SubleagueRepository as SubleagueRepositoryInterface;
use App\Domain\Cycling\Subleague\Subleague;
use Doctrine\ORM\EntityManagerInterface;

final readonly class SubleagueRepository implements SubleagueRepositoryInterface
{
    public function __construct(
        private EntityManagerInterface $entityManager,
    ) {
    }

    public function find(int $id): ?Subleague
    {
        return $this->entityManager->getRepository(Subleague::class)->find($id);
    }

    public function save(Subleague $subleague): void
    {
        $this->entityManager->persist($subleague);
        $this->entityManager->flush();
    }
}
