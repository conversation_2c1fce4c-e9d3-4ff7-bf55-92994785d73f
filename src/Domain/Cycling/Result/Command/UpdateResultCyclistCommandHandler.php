<?php

declare(strict_types=1);

namespace App\Domain\Cycling\Result\Command;

use App\Domain\Cycling\Cyclist\Resolver\CyclistResolver;
use App\Domain\Cycling\Result\Repository\ResultCyclistRepository;
use App\Domain\Cycling\Result\Resolver\ResultCyclistResolver;
use App\Domain\Cycling\Result\ResultCyclist;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
final readonly class UpdateResultCyclistCommandHandler
{
    public function __construct(
        private ResultCyclistRepository $resultCyclistRepository,
        private ResultCyclistResolver $resultCyclistResolver,
        private CyclistResolver $cyclistResolver,
    ) {
    }

    public function __invoke(UpdateResultCyclistCommand $command): ResultCyclist
    {
        $resultCyclist = $this->resultCyclistResolver->resolve($command->id);
        $cyclist = $this->cyclistResolver->resolve($command->cyclistId);

        // Calculate points based on position and ranking type points array
        $points = $this->calculatePoints($resultCyclist->getResult(), $command->position);

        $resultCyclist->update(
            $command->position,
            $cyclist,
            $points,
        );

        $this->resultCyclistRepository->save($resultCyclist);

        return $resultCyclist;
    }

    private function calculatePoints(\App\Domain\Cycling\Result\Result $result, int $position): ?int
    {
        $rankingType = $result->getRankingType();
        $pointsArray = $rankingType->getPoints();

        // Position is 1-based, array is 0-based
        $arrayIndex = $position - 1;

        // Return points if position exists in array, otherwise null
        return isset($pointsArray[$arrayIndex]) ? $pointsArray[$arrayIndex] : null;
    }
}
