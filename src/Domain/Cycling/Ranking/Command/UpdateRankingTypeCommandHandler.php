<?php

declare(strict_types=1);

namespace App\Domain\Cycling\Ranking\Command;

use App\Domain\Cycling\Ranking\RankingType;
use App\Domain\Cycling\Ranking\Repository\RankingTypeRepository;
use App\Domain\Cycling\Ranking\Resolver\RankingResolver;
use App\Domain\Cycling\Ranking\Resolver\RankingTypeResolver;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
final readonly class UpdateRankingTypeCommandHandler
{
    public function __construct(
        private RankingTypeRepository $rankingTypeRepository,
        private RankingTypeResolver $rankingTypeResolver,
        private RankingResolver $rankingResolver,
    ) {
    }

    public function __invoke(UpdateRankingTypeCommand $command): RankingType
    {
        $rankingType = $this->rankingTypeResolver->resolve($command->id);
        $ranking = $this->rankingResolver->resolve($command->rankingId);

        $rankingType->update(
            $command->name,
            $command->points,
            $command->allowResultPrices,
            $ranking,
        );

        $this->rankingTypeRepository->save($rankingType);

        return $rankingType;
    }
}
