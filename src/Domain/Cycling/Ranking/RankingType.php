<?php

declare(strict_types=1);

namespace App\Domain\Cycling\Ranking;

use App\Domain\TimestampableEntity;
use DateTimeImmutable;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity]
#[ORM\Table(name: 'cycling_ranking_type')]
class RankingType
{
    use TimestampableEntity;

    #[ORM\Id]
    #[ORM\Column(type: Types::INTEGER, options: ['unsigned' => true])]
    #[ORM\GeneratedValue]
    private ?int $id = null;

    #[ORM\Column(type: Types::STRING)]
    private string $name;

    /**
     * @var array<int>
     */
    #[ORM\Column(type: Types::SIMPLE_ARRAY)]
    private array $points;

    #[ORM\Column(type: Types::BOOLEAN)]
    private bool $allowResultPrices;

    #[ORM\ManyToOne(targetEntity: Ranking::class, inversedBy: 'rankingTypes')]
    #[ORM\JoinColumn(onDelete: 'CASCADE')]
    private Ranking $ranking;

    /**
     * @param array<int> $points
     */
    public function __construct(
        string $name,
        array $points,
        bool $allowResultPrices,
        Ranking $ranking,
    ) {
        $this->name = $name;
        $this->points = $points;
        $this->allowResultPrices = $allowResultPrices;
        $this->ranking = $ranking;
        $this->createdAt = new DateTimeImmutable();
    }

    /**
     * @param array<int> $points
     */
    public function update(
        string $name,
        array $points,
        bool $allowResultPrices,
        Ranking $ranking,
    ): void {
        $this->name = $name;
        $this->points = $points;
        $this->allowResultPrices = $allowResultPrices;
        $this->ranking = $ranking;
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getName(): string
    {
        return $this->name;
    }

    /**
     * @return array<int>
     */
    public function getPoints(): array
    {
        return $this->points;
    }

    public function getAllowResultPrices(): bool
    {
        return $this->allowResultPrices;
    }

    public function getRanking(): Ranking
    {
        return $this->ranking;
    }

    public function setRanking(Ranking $ranking): void
    {
        $this->ranking = $ranking;
    }

    public function getCreatedAt(): DateTimeImmutable
    {
        return $this->createdAt;
    }

    public function getUpdatedAt(): ?DateTimeImmutable
    {
        return $this->updatedAt;
    }

    public function __toString(): string
    {
        return $this->name;
    }
}
