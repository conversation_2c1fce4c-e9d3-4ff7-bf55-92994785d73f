<?php

declare(strict_types=1);

namespace App\Application\Admin\Request\Input;

use App\Domain\Cycling\Subleague\Subleague;
use App\Domain\Cycling\Tournament\Tournament;
use Symfony\Component\Validator\Constraints as Assert;

final class SubleagueInput
{
    #[Assert\NotBlank]
    #[Assert\Length(max: 255)]
    public ?string $name = null;

    #[Assert\NotBlank]
    public ?Tournament $tournament = null;

    public static function createFromEntity(Subleague $subleague): self
    {
        $input = new self();
        $input->name = $subleague->getName();
        $input->tournament = $subleague->getTournament();

        return $input;
    }
}
