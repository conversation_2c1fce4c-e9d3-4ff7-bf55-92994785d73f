<?php

declare(strict_types=1);

namespace App\Application\Admin\Request\Input;

use App\Domain\Cycling\Ranking\RankingType;
use App\Domain\Cycling\Result\Result;
use App\Domain\Cycling\Stage\Stage;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Symfony\Component\Validator\Constraints as Assert;

final class ResultInput
{
    #[Assert\NotBlank]
    #[Assert\Type(type: 'integer')]
    #[Assert\Positive]
    public int $number;

    #[Assert\NotBlank]
    public ?RankingType $rankingType = null;

    public ?Stage $stage = null;

    /**
     * @var Collection<int, ResultCyclistInput>
     */
    #[Assert\Valid]
    public Collection $cyclists;

    public function __construct()
    {
        $this->cyclists = new ArrayCollection();
    }

    public static function fromEntity(Result $result): self
    {
        $input = new self();
        $input->number = $result->getNumber();
        $input->rankingType = $result->getRankingType();
        $input->stage = $result->getStage();

        foreach ($result->getCyclists() as $resultCyclist) {
            $input->cyclists->add(ResultCyclistInput::fromEntity($resultCyclist));
        }

        return $input;
    }

    /**
     * @return array<int, array{id: ?int, position: int, points: ?int, cyclistId: int}>
     */
    public function getCyclistData(): array
    {
        $cyclistsData = [];

        foreach ($this->cyclists as $cyclistInput) {
            $cyclistData = [
                'id' => $cyclistInput->id,
                'position' => $cyclistInput->position,
                'cyclistId' => $cyclistInput->cyclist?->getId(),
            ];

            $cyclistsData[] = $cyclistData;
        }

        return $cyclistsData;
    }
}
