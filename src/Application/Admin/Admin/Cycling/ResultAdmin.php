<?php

declare(strict_types=1);

namespace App\Application\Admin\Admin\Cycling;

use App\Application\Admin\Controller\CRUDController;
use App\Application\Admin\Form\Type\ResultCyclistInputType;
use App\Application\Admin\LifecycleMiddlewareInterface;
use App\Application\Admin\LifecycleMiddlewareTrait;
use App\Application\Admin\MessageBusAwareTrait;
use App\Application\Admin\Request\Input\ResultInput;
use App\Application\Admin\UnderscoreGlobalLabelStrategy;
use App\Domain\Cycling\Ranking\RankingType;
use App\Domain\Cycling\Result\Command\CreateResultCommand;
use App\Domain\Cycling\Result\Command\UpdateResultCommand;
use App\Domain\Cycling\Result\Result;
use App\Domain\Cycling\Stage\Stage;
use Sonata\AdminBundle\Admin\AbstractAdmin;
use Sonata\AdminBundle\Datagrid\ListMapper;
use Sonata\AdminBundle\Form\FormMapper;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\DependencyInjection\Attribute\Autoconfigure;
use Symfony\Component\Form\Extension\Core\Type\CollectionType;
use Symfony\Component\Form\Extension\Core\Type\IntegerType;

/**
 * @extends AbstractAdmin<Result>
 *
 * @implements LifecycleMiddlewareInterface<Result, ResultInput>
 *
 * @method Result getSubject()
 */
#[Autoconfigure(
    tags: [
        [
            'controller' => CRUDController::class,
            'label' => 'global.label_result',
            'label_translator_strategy' => UnderscoreGlobalLabelStrategy::class,
            'manager_type' => 'orm',
            'model_class' => Result::class,
            'name' => 'sonata.admin',
            'group' => 'admin.group.cycling',
            'translation_domain' => 'admin',
            'show_in_dashboard' => false,
        ],
    ],
    calls: [
        ['setMessageBus', ['@Symfony\\Component\\Messenger\\MessageBusInterface']],
    ],
)]
final class ResultAdmin extends AbstractAdmin implements LifecycleMiddlewareInterface
{
    /** @use LifecycleMiddlewareTrait<Result, ResultInput> */
    use LifecycleMiddlewareTrait;
    use MessageBusAwareTrait;

    protected function generateBaseRouteName(bool $isChildAdmin = false): string
    {
        return 'admin_result';
    }

    protected function generateBaseRoutePattern(bool $isChildAdmin = false): string
    {
        return 'result';
    }

    protected function configureListFields(ListMapper $list): void
    {
        $list
            ->addIdentifier('number')
            ->add('rankingType')
            ->add('stage')
            ->add('createdAt');
    }

    protected function configureFormFields(FormMapper $form): void
    {
        $isChild = $this->isChild();

        $form
            ->with('global.general', ['class' => 'col-md-6'])
            ->add('number', IntegerType::class, [
                'attr' => ['min' => 1],
            ])
            ->add('rankingType', EntityType::class, [
                'class' => RankingType::class,
                'disabled' => $isChild,
                'choice_label' => 'name',
            ])
            ->add('stage', EntityType::class, [
                'class' => Stage::class,
                'required' => false,
                'choice_label' => 'name',
            ])
            ->end();

        if ($this->hasSubject()) {
            $form
                ->with('global.label_cyclist', ['class' => 'col-md-6'])
                ->add('cyclists', CollectionType::class, [
                    'label' => false,
                    'entry_type' => ResultCyclistInputType::class,
                    'allow_add' => true,
                    'allow_delete' => true,
                    'by_reference' => false,
                    'prototype' => true,
                ])
                ->end();
        }

        if ($isChild) {
            $form->get('rankingType')->setData($this->getParent()->getSubject());
        }
    }

    public function lifecycleMiddlewareDispatchCreatable(object $creatable): object
    {
        return $this->lifecycleMiddlewareDispatchMessage($creatable);
    }

    public function lifecycleMiddlewareDispatchEditable(object $editable): object
    {
        return $this->lifecycleMiddlewareDispatchMessage($editable);
    }

    public function lifecycleMiddlewareTransformToCreatable(object $submittedData): object
    {
        // @phpstan-ignore-next-line
        return new CreateResultCommand(
            $submittedData->number,
            $this->getRankingTypeId($submittedData),
            $submittedData->stage?->getId(),
        );
    }

    public function lifecycleMiddlewareTransformToEditable(object $submittedData): object
    {
        $result = $this->getSubject();

        // @phpstan-ignore-next-line
        return new UpdateResultCommand(
            $result->getId(),
            $submittedData->number,
            $this->getRankingTypeId($submittedData),
            $submittedData->stage?->getId(),
            $submittedData->getCyclistData(),
        );
    }

    public function lifecycleMiddlewareFormDataClass(): string
    {
        return ResultInput::class;
    }

    public function lifecycleMiddlewareTransformToFormDataClass(object $entity): object
    {
        return ResultInput::fromEntity($entity);
    }

    private function getRankingTypeId(object $submittedData): ?int
    {
        if ($this->isChild()) {
            $rankingType = $this->getParent()->getSubject();
            if ($rankingType instanceof RankingType) {
                return $rankingType->getId();
            }
        }

        return $submittedData->rankingType?->getId() ?? null;
    }
}
