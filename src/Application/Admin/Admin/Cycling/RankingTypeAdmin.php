<?php

declare(strict_types=1);

namespace App\Application\Admin\Admin\Cycling;

use App\Application\Admin\Controller\CRUDController;
use App\Application\Admin\LifecycleMiddlewareInterface;
use App\Application\Admin\LifecycleMiddlewareTrait;
use App\Application\Admin\MessageBusAwareTrait;
use App\Application\Admin\Request\Input\RankingTypeInput;
use App\Application\Admin\UnderscoreGlobalLabelStrategy;
use App\Domain\Cycling\Ranking\Command\CreateRankingTypeCommand;
use App\Domain\Cycling\Ranking\Command\UpdateRankingTypeCommand;
use App\Domain\Cycling\Ranking\Ranking;
use App\Domain\Cycling\Ranking\RankingType;
use Sonata\AdminBundle\Admin\AbstractAdmin;
use Sonata\AdminBundle\Datagrid\ListMapper;
use Sonata\AdminBundle\Form\FormMapper;
use Sonata\AdminBundle\Form\Type\CollectionType;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\DependencyInjection\Attribute\Autoconfigure;
use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Symfony\Component\Form\Extension\Core\Type\IntegerType;

/**
 * @extends AbstractAdmin<RankingType>
 *
 * @implements LifecycleMiddlewareInterface<RankingType, RankingTypeInput>
 *
 * @method RankingType getSubject()
 */
#[Autoconfigure(
    tags: [
        [
            'controller' => CRUDController::class,
            'label' => 'global.label_ranking_type',
            'label_translator_strategy' => UnderscoreGlobalLabelStrategy::class,
            'manager_type' => 'orm',
            'model_class' => RankingType::class,
            'name' => 'sonata.admin',
            'group' => 'admin.group.cycling',
            'translation_domain' => 'admin',
            'show_in_dashboard' => false,
        ],
    ],
    calls: [
        ['setMessageBus', ['@Symfony\\Component\\Messenger\\MessageBusInterface']],
    ],
)]
final class RankingTypeAdmin extends AbstractAdmin implements LifecycleMiddlewareInterface
{
    /** @use LifecycleMiddlewareTrait<RankingType, RankingTypeInput> */
    use LifecycleMiddlewareTrait;
    use MessageBusAwareTrait;

    protected function generateBaseRouteName(bool $isChildAdmin = false): string
    {
        return 'admin_ranking_type';
    }

    protected function generateBaseRoutePattern(bool $isChildAdmin = false): string
    {
        return 'ranking-type';
    }

    protected function configureListFields(ListMapper $list): void
    {
        $list
            ->addIdentifier('name')
            ->add('ranking')
            ->add('allowResultPrices', null, [
                'label' => 'global.label_allow_result_prices',
            ])
            ->add('createdAt');
    }

    protected function configureFormFields(FormMapper $form): void
    {
        $isChild = $this->isChild();

        $form
            ->with('global.general', ['class' => 'col-md-6'])
            ->add('name')
            ->add('points', CollectionType::class, [
                'label' => 'global.label_points',
                'entry_type' => IntegerType::class,
                'allow_add' => true,
                'allow_delete' => true,
            ])
            ->add('allowResultPrices', CheckboxType::class, [
                'label' => 'global.label_allow_result_prices',
                'required' => false,
            ])
            ->add('ranking', EntityType::class, [
                'class' => Ranking::class,
                'disabled' => $isChild,
                'choice_label' => 'name',
            ])
            ->end();

        if ($isChild) {
            $form->get('ranking')->setData($this->getParent()->getSubject());
        }
    }

    public function lifecycleMiddlewareDispatchCreatable(object $creatable): object
    {
        return $this->lifecycleMiddlewareDispatchMessage($creatable);
    }

    public function lifecycleMiddlewareDispatchEditable(object $editable): object
    {
        return $this->lifecycleMiddlewareDispatchMessage($editable);
    }

    public function lifecycleMiddlewareTransformToCreatable(object $submittedData): object
    {
        // @phpstan-ignore-next-line
        return new CreateRankingTypeCommand(
            $submittedData->name,
            $submittedData->points,
            $submittedData->allowResultPrices,
            $this->getRankingId($submittedData),
        );
    }

    public function lifecycleMiddlewareTransformToEditable(object $submittedData): object
    {
        $rankingType = $this->getSubject();

        // @phpstan-ignore-next-line
        return new UpdateRankingTypeCommand(
            $rankingType->getId(),
            $submittedData->name,
            $submittedData->points,
            $submittedData->allowResultPrices,
            $this->getRankingId($submittedData),
        );
    }

    public function lifecycleMiddlewareFormDataClass(): string
    {
        return RankingTypeInput::class;
    }

    public function lifecycleMiddlewareTransformToFormDataClass(object $entity): object
    {
        return RankingTypeInput::createFromEntity($entity);
    }

    private function getRankingId(object $submittedData): ?int
    {
        if ($this->isChild()) {
            $ranking = $this->getParent()->getSubject();
            if ($ranking instanceof Ranking) {
                return $ranking->getId();
            }
        }

        return $submittedData->ranking?->getId() ?? null;
    }
}
