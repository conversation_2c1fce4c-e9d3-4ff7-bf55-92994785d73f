<?php

declare(strict_types=1);

namespace App\Domain\Cycling\Subleague\Command;

use App\Domain\Cycling\Subleague\Repository\SubleagueRepository;
use App\Domain\Cycling\Subleague\Resolver\SubleagueResolver;
use App\Domain\Cycling\Subleague\Subleague;
use App\Domain\Cycling\Tournament\Resolver\TournamentResolver;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
final readonly class UpdateSubleagueCommandHandler
{
    public function __construct(
        private SubleagueRepository $subleagueRepository,
        private SubleagueResolver $subleagueResolver,
        private TournamentResolver $tournamentResolver,
    ) {
    }

    public function __invoke(UpdateSubleagueCommand $command): Subleague
    {
        $subleague = $this->subleagueResolver->resolve($command->id);
        $tournament = $this->tournamentResolver->resolve($command->tournamentId);

        $subleague->update(
            $command->name,
            $tournament,
        );

        $this->subleagueRepository->save($subleague);

        return $subleague;
    }
}
