<?php

declare(strict_types=1);

namespace App\Application\Admin\Admin\Cycling;

use App\Application\Admin\Controller\CRUDController;
use App\Application\Admin\LifecycleMiddlewareInterface;
use App\Application\Admin\LifecycleMiddlewareTrait;
use App\Application\Admin\MessageBusAwareTrait;
use App\Application\Admin\Request\Input\RankingTypeInput;
use App\Application\Admin\UnderscoreGlobalLabelStrategy;
use App\Domain\Cycling\Ranking\Command\CreateRankingTypeCommand;
use App\Domain\Cycling\Ranking\Command\UpdateRankingTypeCommand;
use App\Domain\Cycling\Ranking\Ranking;
use App\Domain\Cycling\Ranking\RankingType;
use Sonata\AdminBundle\Admin\AbstractAdmin;
use Sonata\AdminBundle\Datagrid\ListMapper;
use Sonata\AdminBundle\Form\FormMapper;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\DependencyInjection\Attribute\Autoconfigure;
use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Symfony\Component\Form\Extension\Core\Type\TextType;

/**
 * @extends AbstractAdmin<RankingType>
 *
 * @implements LifecycleMiddlewareInterface<RankingType, RankingTypeInput>
 *
 * @method RankingType getSubject()
 */
#[Autoconfigure(
    tags: [
        [
            'controller' => CRUDController::class,
            'label' => 'global.label_ranking_type',
            'label_translator_strategy' => UnderscoreGlobalLabelStrategy::class,
            'manager_type' => 'orm',
            'model_class' => RankingType::class,
            'name' => 'sonata.admin',
            'group' => 'admin.group.cycling',
            'translation_domain' => 'admin',
        ],
    ],
    calls: [
        ['setMessageBus', ['@Symfony\\Component\\Messenger\\MessageBusInterface']],
    ],
)]
final class RankingTypeAdmin extends AbstractAdmin implements LifecycleMiddlewareInterface
{
    /** @use LifecycleMiddlewareTrait<RankingType, RankingTypeInput> */
    use LifecycleMiddlewareTrait;
    use MessageBusAwareTrait;

    protected function generateBaseRouteName(bool $isChildAdmin = false): string
    {
        return 'admin_ranking_type';
    }

    protected function generateBaseRoutePattern(bool $isChildAdmin = false): string
    {
        return 'ranking-type';
    }

    protected function configureListFields(ListMapper $list): void
    {
        $list
            ->addIdentifier('name')
            ->add('ranking')
            ->add('allowResultPrices', null, [
                'label' => 'global.label_allow_result_prices',
            ])
            ->add('createdAt');
    }

    protected function configureFormFields(FormMapper $form): void
    {
        $isChild = $this->isChild();

        $form
            ->with('global.general', ['class' => 'col-md-6'])
            ->add('name')
            ->add('points', TextType::class, [
                'label' => 'global.label_points',
                'help' => 'global.help_points_comma_separated',
                'attr' => [
                    'placeholder' => '50,30,20,15,10,8,6,4,2,1',
                ],
            ])
            ->add('allowResultPrices', CheckboxType::class, [
                'label' => 'global.label_allow_result_prices',
                'required' => false,
            ])
            ->add('ranking', EntityType::class, [
                'class' => Ranking::class,
                'disabled' => $isChild,
                'choice_label' => 'name',
            ])
            ->end();

        if ($isChild) {
            $form->get('ranking')->setData($this->getParent()->getSubject());
        }
    }

    public function lifecycleMiddlewareDispatchCreatable(object $creatable): object
    {
        return $this->lifecycleMiddlewareDispatchMessage($creatable);
    }

    public function lifecycleMiddlewareDispatchEditable(object $editable): object
    {
        return $this->lifecycleMiddlewareDispatchMessage($editable);
    }

    public function lifecycleMiddlewareTransformToCreatable(object $submittedData): object
    {
        // @phpstan-ignore-next-line
        return new CreateRankingTypeCommand(
            $submittedData->name,
            $this->parsePoints($submittedData->points),
            $submittedData->allowResultPrices,
            $this->getRankingId($submittedData),
        );
    }

    public function lifecycleMiddlewareTransformToEditable(object $submittedData): object
    {
        $rankingType = $this->getSubject();

        // @phpstan-ignore-next-line
        return new UpdateRankingTypeCommand(
            $rankingType->getId(),
            $submittedData->name,
            $this->parsePoints($submittedData->points),
            $submittedData->allowResultPrices,
            $this->getRankingId($submittedData),
        );
    }

    public function lifecycleMiddlewareFormDataClass(): string
    {
        return RankingTypeInput::class;
    }

    public function lifecycleMiddlewareTransformToFormDataClass(object $entity): object
    {
        $input = RankingTypeInput::createFromEntity($entity);
        
        // Convert points array to comma-separated string for form display
        $input->points = implode(',', $input->points);

        return $input;
    }

    private function getRankingId(object $submittedData): ?int
    {
        if ($this->isChild()) {
            $ranking = $this->getParent()->getSubject();
            if ($ranking instanceof Ranking) {
                return $ranking->getId();
            }
        }

        return $submittedData->ranking?->getId() ?? null;
    }

    /**
     * @param string|array<int> $points
     * @return array<int>
     */
    private function parsePoints(string|array $points): array
    {
        if (is_array($points)) {
            return $points;
        }

        if (empty($points)) {
            return [];
        }

        return array_map('intval', array_filter(array_map('trim', explode(',', $points))));
    }
}
