<?php

declare(strict_types=1);

namespace App\Domain\Cycling\Tournament;

use App\Domain\Cycling\Ranking\Ranking;
use App\Domain\Cycling\Stage\Stage;
use App\Domain\Cycling\Subleague\Subleague;
use App\Domain\Cycling\Team\Team;
use App\Domain\TimestampableEntity;
use App\Domain\Website\Website;
use DateTimeImmutable;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity]
#[ORM\Table(name: 'cycling_tournament')]
class Tournament
{
    use TimestampableEntity;

    #[ORM\Id]
    #[ORM\Column(type: Types::INTEGER, options: ['unsigned' => true])]
    #[ORM\GeneratedValue]
    private ?int $id = null;

    #[ORM\Column(type: Types::STRING)]
    private string $name;

    #[ORM\Column(type: Types::DATETIME_IMMUTABLE)]
    private ?DateTimeImmutable $startsAt;

    #[ORM\Column(type: Types::DATETIME_IMMUTABLE)]
    private ?DateTimeImmutable $endsAt;

    #[ORM\Column(type: Types::DATETIME_IMMUTABLE, nullable: true)]
    private ?DateTimeImmutable $registrationStartsAt;

    #[ORM\Column(type: Types::DATETIME_IMMUTABLE, nullable: true)]
    private ?DateTimeImmutable $registrationEndsAt;

    #[ORM\Column(type: Types::INTEGER)]
    private int $year;

    #[ORM\Column(type: Types::INTEGER, nullable: true)]
    private ?int $externalId;

    #[ORM\Column(type: Types::INTEGER)]
    private ?int $nrOfCyclists;

    #[ORM\Column(type: Types::INTEGER)]
    private ?int $nrOfReserves;

    #[ORM\ManyToOne(targetEntity: Website::class)]
    private Website $website;

    /**
     * @var Collection<int, Stage>
     */
    #[ORM\OneToMany(targetEntity: Stage::class, mappedBy: 'tournament')]
    private Collection $stages;

    /**
     * @var Collection<int, Team>
     */
    #[ORM\OneToMany(targetEntity: Team::class, mappedBy: 'tournament')]
    private Collection $teams;

    /**
     * @var Collection<int, Subleague>
     */
    #[ORM\OneToMany(targetEntity: Subleague::class, mappedBy: 'tournament')]
    private Collection $subleagues;

    public function __construct(
        string $name,
        ?DateTimeImmutable $startsAt,
        ?DateTimeImmutable $endsAt,
        ?DateTimeImmutable $registrationStartsAt,
        ?DateTimeImmutable $registrationEndsAt,
        int $year,
        ?int $externalId,
        ?int $nrOfCyclists,
        ?int $nrOfReserves,
        Website $website,
    ) {
        $this->name = $name;
        $this->startsAt = $startsAt;
        $this->endsAt = $endsAt;
        $this->registrationStartsAt = $registrationStartsAt;
        $this->registrationEndsAt = $registrationEndsAt;
        $this->year = $year;
        $this->externalId = $externalId;
        $this->nrOfCyclists = $nrOfCyclists;
        $this->nrOfReserves = $nrOfReserves;
        $this->website = $website;
        $this->stages = new ArrayCollection();
        $this->teams = new ArrayCollection();
        $this->subleagues = new ArrayCollection();
        $this->createdAt = new DateTimeImmutable();
    }

    public function update(
        string $name,
        DateTimeImmutable $startsAt,
        DateTimeImmutable $endsAt,
        ?DateTimeImmutable $registrationStartsAt,
        ?DateTimeImmutable $registrationEndsAt,
        int $year,
        ?int $externalId,
        ?int $nrOfCyclists,
        ?int $nrOfReserves,
        Website $website,
    ): void {
        $this->name = $name;
        $this->startsAt = $startsAt;
        $this->endsAt = $endsAt;
        $this->registrationStartsAt = $registrationStartsAt;
        $this->registrationEndsAt = $registrationEndsAt;
        $this->year = $year;
        $this->externalId = $externalId;
        $this->nrOfCyclists = $nrOfCyclists;
        $this->nrOfReserves = $nrOfReserves;
        $this->website = $website;
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function getStartsAt(): ?DateTimeImmutable
    {
        return $this->startsAt;
    }

    public function getEndsAt(): ?DateTimeImmutable
    {
        return $this->endsAt;
    }

    public function getRegistrationStartsAt(): ?DateTimeImmutable
    {
        return $this->registrationStartsAt;
    }

    public function getRegistrationEndsAt(): ?DateTimeImmutable
    {
        return $this->registrationEndsAt;
    }

    public function getYear(): int
    {
        return $this->year;
    }

    public function getExternalId(): ?int
    {
        return $this->externalId;
    }

    public function getNrOfCyclists(): ?int
    {
        return $this->nrOfCyclists;
    }

    public function getNrOfReserves(): ?int
    {
        return $this->nrOfReserves;
    }

    public function __toString(): string
    {
        return $this->name;
    }

    public function getWebsite(): Website
    {
        return $this->website;
    }

    /**
     * @return Collection<int, Stage>
     */
    public function getStages(): Collection
    {
        return $this->stages;
    }

    /**
     * @return Collection<int, Team>
     */
    public function getTeams()
    {
        return $this->teams;
    }

    /**
     * @return Collection<int, Subleague>
     */
    public function getSubleagues(): Collection
    {
        return $this->subleagues;
    }

    public function getCreatedAt(): DateTimeImmutable
    {
        return $this->createdAt;
    }
}
