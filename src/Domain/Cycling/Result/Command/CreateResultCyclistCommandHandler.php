<?php

declare(strict_types=1);

namespace App\Domain\Cycling\Result\Command;

use App\Domain\Cycling\Cyclist\Resolver\CyclistResolver;
use App\Domain\Cycling\Result\Repository\ResultCyclistRepository;
use App\Domain\Cycling\Result\Resolver\ResultResolver;
use App\Domain\Cycling\Result\ResultCyclist;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
final readonly class CreateResultCyclistCommandHandler
{
    public function __construct(
        private ResultCyclistRepository $resultCyclistRepository,
        private ResultResolver $resultResolver,
        private CyclistResolver $cyclistResolver,
    ) {
    }

    public function __invoke(CreateResultCyclistCommand $command): ResultCyclist
    {
        $result = $this->resultResolver->resolve($command->resultId);
        $cyclist = $this->cyclistResolver->resolve($command->cyclistId);

        // Calculate points based on position and ranking type points array
        $points = $this->calculatePoints($result, $command->position);

        $resultCyclist = new ResultCyclist(
            $command->position,
            $cyclist,
            $points,
            $result,
        );

        $result->addCyclist($resultCyclist);
        $this->resultCyclistRepository->save($resultCyclist);

        return $resultCyclist;
    }

    private function calculatePoints(\App\Domain\Cycling\Result\Result $result, int $position): ?int
    {
        $rankingType = $result->getRankingType();
        $pointsArray = $rankingType->getPoints();

        // Position is 1-based, array is 0-based
        $arrayIndex = $position - 1;

        // Return points if position exists in array, otherwise null
        return isset($pointsArray[$arrayIndex]) ? $pointsArray[$arrayIndex] : null;
    }
}
