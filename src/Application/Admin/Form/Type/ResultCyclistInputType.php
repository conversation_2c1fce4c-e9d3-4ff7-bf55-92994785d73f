<?php

declare(strict_types=1);

namespace App\Application\Admin\Form\Type;

use App\Application\Admin\Request\Input\ResultCyclistInput;
use App\Domain\Cycling\Cyclist\Cyclist;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\HiddenType;
use Symfony\Component\Form\Extension\Core\Type\IntegerType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Contracts\Translation\TranslatorInterface;

final class ResultCyclistInputType extends AbstractType
{
    public function __construct(private readonly TranslatorInterface $translator)
    {
    }

    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('id', HiddenType::class)
            ->add('position', IntegerType::class, [
                'label' => false,
                'attr' => [
                    'placeholder' => $this->translator->trans('global.label_position', [], 'admin'),
                    'min' => 1,
                ],
            ])
            ->add('points', IntegerType::class, [
                'label' => false,
                'required' => false,
                'disabled' => true,
                'attr' => [
                    'placeholder' => $this->translator->trans('global.label_points_auto', [], 'admin'),
                    'title' => $this->translator->trans('global.help_points_auto_calculated', [], 'admin'),
                ],
            ])
            ->add('cyclist', EntityType::class, [
                'class' => Cyclist::class,
                'choice_label' => 'fullName',
                'label' => false,
                'placeholder' => $this->translator->trans('global.label_select_cyclist', [], 'admin'),
            ]);
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => ResultCyclistInput::class,
        ]);
    }

    public function getBlockPrefix(): string
    {
        return 'admin_result_cyclist_input';
    }
}
