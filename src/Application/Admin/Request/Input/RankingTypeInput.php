<?php

declare(strict_types=1);

namespace App\Application\Admin\Request\Input;

use App\Domain\Cycling\Ranking\Ranking;
use App\Domain\Cycling\Ranking\RankingType;
use Symfony\Component\Validator\Constraints as Assert;

final class RankingTypeInput
{
    #[Assert\NotBlank]
    #[Assert\Length(max: 255)]
    public ?string $name = null;

    #[Assert\NotBlank]
    public string $points = '';

    public bool $allowResultPrices = false;

    #[Assert\NotBlank]
    public ?Ranking $ranking = null;

    public static function createFromEntity(RankingType $rankingType): self
    {
        $input = new self();
        $input->name = $rankingType->getName();
        $input->points = $rankingType->getPoints();
        $input->allowResultPrices = $rankingType->getAllowResultPrices();
        $input->ranking = $rankingType->getRanking();

        return $input;
    }
}
