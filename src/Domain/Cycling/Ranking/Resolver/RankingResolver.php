<?php

declare(strict_types=1);

namespace App\Domain\Cycling\Ranking\Resolver;

use App\Domain\Assert;
use App\Domain\Cycling\Ranking\Ranking;
use App\Domain\Cycling\Ranking\Repository\RankingRepository;

final readonly class RankingResolver
{
    public function __construct(private RankingRepository $repository)
    {
    }

    public function resolve(int $id): Ranking
    {
        $ranking = $this->repository->find($id);

        Assert::isInstanceOf($ranking, Ranking::class, 'ranking not found');

        return $ranking;
    }
}
